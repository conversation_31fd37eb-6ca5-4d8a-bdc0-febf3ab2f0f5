import { MidwayConfig } from '@midwayjs/core';

export default {
  sequelize: {
    dataSource: {
      default: {
        dialect: 'mysql',
        host: '**********',
        port: 3306,
        username: 'root',
        password: 'Ysp@1234',
        database: 'teacher-evaluation-question',
        // 模型设置
        define: {
          timestamps: true,
          createdAt: 'createdAt',
          updatedAt: 'updatedAt',
          // 禁用驼峰转换
          underscored: false,
          charset: 'utf8mb4',
          collate: 'utf8mb4_bin',
        },
        // 时区设置
        timezone: '+08:00',
        // 连接池配置
        pool: {
          max: 5,
          min: 0,
          idle: 10000,
          acquire: 30000, // 获取连接的最大等待时间
          evict: 1000, // 检查空闲连接的间隔时间
        },
        entities: ['entity'],
        logging: false,
        repositoryMode: true,
        // 禁用自动同步，避免约束冲突
        sync: false,
      },
    },
  },
  axios: {
    clients: {
      apiManager: {
        baseURL: 'http://*************:1002',
        timeout: 10000,
      },
    },
  },
  // JWT认证组件配置
  jwtAuth: {
    enable: true,
    // 只配置需要覆盖的项
    apiManagerBaseURL: 'http://*************:1002', // 根据实际环境修改
  },
} as MidwayConfig;
